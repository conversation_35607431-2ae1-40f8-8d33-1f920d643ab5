// <copyright file="IMasterClientsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.DataManager.LegalEntityRelations.MasterClients.RequestResponses;
using NetProGroup.Trust.DataManager.MasterClients.RequestResponses;

namespace NetProGroup.Trust.DataManager.MasterClients
{
    /// <summary>
    /// Interface for the datamanager for Jurisdictions.
    /// </summary>
    public interface IMasterClientsDataManager : IScopedService
    {
        /// <summary>
        /// Creates the MasterClient from the model.
        /// </summary>
        /// <param name="model">The inbound model.</param>
        /// <param name="saveChanges">Whether  to save the changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<MasterClientDTO> CreateMasterClientAsync(CreateMasterClientDTO model, bool saveChanges = false);

        /// <summary>
        /// Updates the MasterClient from the model.
        /// </summary>
        /// <param name="model">The inbound model.</param>
        /// <param name="saveChanges">Whether to save the changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<MasterClientDTO> UpdateMasterClientAsync(UpdateMasterClientDTO model, bool saveChanges = false);

        /// <summary>
        /// Gets a single masterclient.
        /// </summary>
        /// <param name="masterclientId">Id of the masterclient to get.</param>
        /// <returns>A <see cref="Task{MasterClientDTO}"/> representing the asynchronous operation.</returns>
        Task<MasterClientDTO> GetMasterClientAsync(Guid masterclientId);

        /// <summary>
        /// Imports the MasterClient and creates / links users by email address.
        /// </summary>
        /// <param name="request">The request with all parameters.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task ImportMasterClients(CreateMasterClientsRequest request);

        /// <summary>
        /// Checks if the email/masterclientcode relation exists.
        /// </summary>
        /// <param name="masterClientCode">The MasterClientCode to check for.</param>
        /// <param name="email">The emailaddress to check for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<bool> EmailExistsForMasterClientCodeAsync(string masterClientCode, string email);

        /// <summary>
        /// Gets a paged list with master clients.
        /// </summary>
        /// <param name="request">Request with optional parameters to search for.</param>
        /// <returns>A <see cref="Task{ListMasterClientsResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<ListMasterClientsResponse> ListMasterClientsAsync(ListMasterClientsRequest request);

        /// <summary>
        /// Gets a list with master clients.
        /// </summary>
        /// <param name="request">Request with parameters to search for.</param>
        /// <returns>A <see cref="Task{SearchMasterClientsResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<SearchMasterClientsResponse> SearchMasterClientsAsync(SearchMasterClientsRequest request);

        /// <summary>
        /// Syncs the MasterClient and creates / links users by email address.
        /// </summary>
        /// <param name="request">The request with all parameters.</param>
        /// <param name="jobLock">The lock to use for the operation.</param>
        /// <param name="beforeCommitAsync">The action to execute before committing the changes.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SyncMasterClientsAsync(SyncMasterClientRequest request, LockDTO jobLock = null, Func<DbContext, Task> beforeCommitAsync = null);

        /// <summary>
        /// Manually adds a user to a master client.
        /// </summary>
        /// <param name="model">The data transfer object containing the user and master client information.</param>
        /// <param name="saveChanges">Denotes wehther to save the changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<MasterClientUserDTO> CreateUserToMasterClientAsync(CreateMasterClientUserDTO model, bool saveChanges = false);

        /// <summary>
        /// Removes a user from a master client.
        /// </summary>
        /// <param name="model">The data transfer object containing the user and master client information.</param>
        /// <param name="saveChanges">Whether to save the changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RemoveUserFromMasterClientAsync(RemoveMasterClientUserDTO model, bool saveChanges = false);
    }
}
