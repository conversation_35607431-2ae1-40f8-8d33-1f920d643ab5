// <copyright file="CompanyImport.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.LegalEntities.Models;
using NetProGroup.Trust.Domain.Sync;
using NetProGroup.Trust.Import.Interfaces;

namespace NetProGroup.Trust.Import.Importers
{
    /// <summary>
    /// Basic implementation for importing Company data.
    /// </summary>
    public class CompanyImport : ImportBase, ICompanyImport
    {
        private readonly ILogger _logger;
        private readonly ILegalEntitiesDataManager _legalEntitiesDataManager;
        private readonly ISyncCompanyRepository _syncCompanyRepository;
        private readonly ILockManager _lockManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="CompanyImport"/> class.
        /// </summary>
        public CompanyImport(
            ILogger<CompanyImport> logger,
            ILegalEntitiesDataManager legalEntitiesDataManager,
            ISyncCompanyRepository syncCompanyRepository,
            ILockManager lockManager)
        {
            _logger = logger;
            _legalEntitiesDataManager = legalEntitiesDataManager;
            _syncCompanyRepository = syncCompanyRepository;
            _lockManager = lockManager;
        }

        /// <summary>
        /// Validates the contents of the file.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to import for.</param>
        /// <param name="stream">The stream holding the file.</param>
        /// <returns></returns>
        public virtual async Task<object> ImportFileAsync(Guid jurisdictionId, Stream stream)
        {
            // Build the canonical model from the excel sheet
            var canonicalModels = BuildCanonicalModels(stream);

            // Create the request to use on the DataManager
            var request = new SyncLegalEntitiesRequest();

            var legalEntities = new Dictionary<string, SyncLegalEntity>(StringComparer.OrdinalIgnoreCase);

            foreach (var item in canonicalModels)
            {
                if (!legalEntities.TryGetValue(item.MasterClientCode, out SyncLegalEntity createLegalEntity))
                {
                    createLegalEntity = new SyncLegalEntity
                    {
                        MasterClientCode = item.MasterClientCode,
                        EntityType = DomainShared.Enums.LegalEntityType.Company,
                        Code = item.Code,
                        Name = item.Name,
                        IncorporationNr = item.IncorporationNr,
                        IncorporationDate = item.IncorporationDate,
                        ReferralOffice = item.ReferralOffice
                    };
                    legalEntities.Add(item.Code, createLegalEntity);
                }
            }

            legalEntities.Values.ToList().ForEach(x => request.LegalEntities.Add(x));

            await _legalEntitiesDataManager.SyncLegalEntitiesAsync(request);

            return null;
        }

        /// <summary>
        /// Imports the file and saves the data.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to import for.</param>
        /// <param name="stream">The stream holding the file.</param>
        /// <returns></returns>
        public virtual async Task<object> ValidateFileAsync(Guid jurisdictionId, Stream stream)
        {
            await Task.CompletedTask;
            throw new NotImplementedException();
        }

        /// <inheritdoc/>
        public async Task<SyncResult> SyncViewPointAsync(LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(jobLock, nameof(jobLock));

            await _lockManager.RefreshLockAsync(jobLock.Id.Value);

            var result = new SyncResult();

            if (!await _syncCompanyRepository.StagingTableExistsAsync())
            {
                return result;
            }

            var data = await _syncCompanyRepository.GetChangedCompaniesAsync();
            
            // Set the count of changed records
            result.UpdatedCount = data.Count;

            var legalEntities = new List<SyncLegalEntity>();

            // Do sync
            // - Make canonical data and let datamanager do the changes

            foreach (var item in data)
            {
                var legalEntity = new SyncLegalEntity
                {
                    UniqueId = item.EntityUniqueNr.ToString(),
                    JurisdictionVPCode = item.JurisdictionCode,
                    JurisdictionCode = MapJurisdictionToJurisdictionCode(item.JurisdictionCode),
                    MasterClientCode = item.ClientCode,
                    MasterClientName = item.ClientName,
                    EntityType = DomainShared.Enums.LegalEntityType.Company,
                    Name = item.EntityName,
                    Code = item.EntityCode,
                    LegacyCode = item.EntityLegacyID,
                    EntityStatusCode = item.EntityStatusCode,
                    EntityStatus = item.EntityStatus,
                    EntitySubStatusCode = item.EntitySubStatusCode,
                    EntitySubStatus = item.EntitySubStatus,
                    EntityTypeCode = item.EntityTypeCode,
                    EntityTypeName = item.EntityType,
                    Administrator = item.Administrator,
                    Manager = item.Manager,
                    ReferralOffice = item.ReferralOfficeCode,
                    ProductionOffice = item.ProductionOffice,
                    RiskGroup = item.RiskGroup,
                    IncorporationNr = item.IncorporationNumber,
                    IncorporationDate = item.IncorporationDate
                };

                legalEntities.Add(legalEntity);
            }

            var request = new SyncLegalEntitiesRequest
            {
                LegalEntities = legalEntities,
            };

            await _legalEntitiesDataManager.SyncLegalEntitiesAsync(request, jobLock,
                beforeCommitAsync: async (dbContext) =>
                {
                    await _lockManager.RefreshLockAsync(jobLock.Id.Value);

                    _logger.LogInformation("Copy data to history...");
                    await _syncCompanyRepository.SaveLastStateAsync();
                    _logger.LogInformation("Copied to history");
                });

            return result;
        }

        /// <summary>
        /// Parses the excel sheet and return a list of canonical Companies.
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        protected virtual ICollection<CanonicalModels.Company> BuildCanonicalModels(Stream stream)
        {
            var result = new List<CanonicalModels.Company>();

            // Create an instance of Fast Excel with the stream as the input.
            using (FastExcel.FastExcel fastExcel = new FastExcel.FastExcel(null, stream, true, true))
            {
                var sheet = fastExcel.Worksheets[0];
                sheet.Read();

                // Get all rows after the header row
                var rows = sheet.Rows.Skip(1).ToArray();

                foreach (var row in rows)
                {
                    var companyCode = GetCellValueAsString(sheet, row, "Company Code", false);

                    if (!string.IsNullOrEmpty(companyCode))
                    {
                        var name = GetCellValueAsString(sheet, row, "Name", false);
                        var referral = GetCellValueAsString(sheet, row, "Referral", false);

                        var masterClientCode = GetCellValueAsString(sheet, row, "MC Code", false);
                        var incorporationNr = GetCellValueAsString(sheet, row, "Incorporation Number", false);
                        var incorporationDate = GetCellValueAsDateTime(sheet, row, "Incorporation Date", false);

                        Check.NotNullOrWhiteSpace(companyCode, "Company Code");
                        Check.NotNullOrWhiteSpace(name, "Name");
                        Check.NotNullOrWhiteSpace(masterClientCode, "MC Code");
                        Check.NotNullOrWhiteSpace(incorporationNr, "Incorporation Number");
                        Check.NotNull(incorporationDate, "Incorporation Date");

                        var company = new CanonicalModels.Company
                        {
                            Code = companyCode,
                            Name = name,
                            ReferralOffice = referral,
                            MasterClientCode = masterClientCode,
                            IncorporationNr = incorporationNr,
                            IncorporationDate = incorporationDate.Value
                        };

                        result.Add(company);
                    }
                }
            }

            return result;
        }

        private static DateTime ToDate(string value)
        {
            var tags = value.Split('/');
            return new DateTime(int.Parse(tags[2]), int.Parse(tags[1]), int.Parse(tags[0]));
        }

        /// <summary>
        /// Masp the given jurisdiction to an existing normalized code.
        /// </summary>
        /// <param name="jurisdiction">The jurisdiction from file.</param>
        /// <returns>The found code.</returns>
        private static string MapJurisdictionToJurisdictionCode(string jurisdiction)
        {
            if (string.IsNullOrEmpty(jurisdiction))
            {
                return string.Empty;
            }

            return DataManager.Jurisdictions.CodeConverter.ViewPointCodeToPCPCode(jurisdiction);
        }
    }
}
