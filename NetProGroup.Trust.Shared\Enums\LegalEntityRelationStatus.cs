// <copyright file="LegalEntityRelationStatus.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DomainShared.Enums
{
    /// <summary>
    /// Enumeration representing the type of a legal entity.
    /// </summary>
    public enum LegalEntityRelationStatus
    {
        /// <summary>
        /// The status is 'Initial'.
        /// </summary>
        /// <remarks>
        /// A relation gets this status when it is received for the first time.
        /// </remarks>
        Initial = 0,

        /// <summary>
        /// The status is 'Refreshed'.
        /// </summary>
        /// <remarks>
        /// A relation gets this status when it was updated while having status 'Initial'.
        /// </remarks>
        Refreshed = 1,

        /// <summary>
        /// The status is 'Confirmed'.
        /// </summary>
        /// <remarks>
        /// A relation gets this status when a user confirmed the data.
        /// </remarks>
        Confirmed = 2,

        /// <summary>
        /// The status is 'PendingUpdateRequest'.
        /// </summary>
        /// <remarks>
        /// A relation gets this status when a user has requested an update of the data.
        /// </remarks>
        PendingUpdateRequest = 3,

        /// <summary>
        /// The status is 'DataReceived'.
        /// </summary>
        /// <remarks>
        /// A relation gets this status when it was in status 'Confirmed' or 'PendingUpdateRequest' and an update was received.
        /// </remarks>
        UpdateReceived = 4,
    }
}