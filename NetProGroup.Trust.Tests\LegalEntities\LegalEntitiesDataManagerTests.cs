using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Models;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Tests.Shared;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.DataManager.LegalEntities.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Shared.Enums;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Framework.Services.ActivityLogs.EFRepository;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Trust.DataManager.LegalEntities.Models;
using NetProGroup.Trust.Shared.Jurisdictions;
using NetProGroup.Trust.Domain.Shared.Defines;

namespace NetProGroup.Trust.Tests.LegalEntities
{
    [TestFixture]
    public class LegalEntitiesDataManagerTests : TestBase
    {
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private ILegalEntitiesDataManager _legalEntitiesDataManager;
        private ISettingsManager _settingsManager;
        private IJurisdictionsRepository _jurisdictionsRepository;
        private IMasterClientsRepository _masterClientsRepository;
        private ILegalEntityHistoryRepository _legalEntityHistoryRepository;
        private List<Guid> _jurisdictionIDs;
        private IActivityLogRepository _activityLogRepository;

        [SetUp]
        public void Setup()
        {
            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            _legalEntitiesDataManager = _server.Services.GetRequiredService<ILegalEntitiesDataManager>();
            _jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            _masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
            _legalEntityHistoryRepository = _server.Services.GetRequiredService<ILegalEntityHistoryRepository>();
            _activityLogRepository = _server.Services.GetRequiredService<IActivityLogRepository>();
            _settingsManager = _server.Services.GetRequiredService<ISettingsManager>();
            _jurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList();
        }

        #region ListCompaniesAsync Tests
        [Test]
        public async Task ListCompaniesAsync_FiltersByOnboardingStatus()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1", onboardingStatus: OnboardingStatus.Onboarding),
                CreateTestLegalEntity("Company 2", "C2", "MC2", onboardingStatus: OnboardingStatus.Approved),
                CreateTestLegalEntity("Company 3", "C3", "MC3", onboardingStatus: OnboardingStatus.Declined)
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = new ListCompaniesRequest
            {
                OnboardingStatuses = new List<OnboardingStatus> { OnboardingStatus.Approved },
                PageNumber = 1,
                PageSize = 10,
                AuthorizedJurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList()
            };

            // Act
            var result = await _legalEntitiesDataManager.ListCompaniesAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.CompanyItems.Should().NotBeNull();
            result.CompanyItems.Should().HaveCount(1);
            var companyDTO = result.CompanyItems.Single();
            companyDTO.Name.Should().Be("Company 2");
            companyDTO.OnboardingStatus.Should().Be(OnboardingStatus.Approved);
        }

        [Test]
        public async Task ListCompaniesAsync_FiltersPaginatesAndReturnsCorrectResults()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Alpha Company", "AC1", "MC1", "LC1", 2020),
                CreateTestLegalEntity("Beta Corporation", "BC1", "MC2", "LC2", 2021, "23456", "REF2"),
                CreateTestLegalEntity("Gamma Corporation", "GE1", "MC3", "LC3", 2022),
                CreateTestLegalEntity("Delta Corporation", "DI1", "MC4", "LC4", 2023)
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = new ListCompaniesRequest
            {
                SearchTerm = "Corp",
                Active = true,
                PageNumber = 1,
                PageSize = 2,
                AuthorizedJurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList()
            };

            // Act
            var result = await _legalEntitiesDataManager.ListCompaniesAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.CompanyItems.Should().NotBeNull();
            result.CompanyItems.Should().HaveCount(2);

            var company = result.CompanyItems.First();
            company.Name.Should().Be("Beta Corporation");
            company.Code.Should().Be("BC1");
            company.LegacyCode.Should().Be("LC2");
            company.IncorporationNumber.Should().Be("23456");
            company.IncorporationDate.Year.Should().Be(2021);
            company.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding);
            company.MasterClientCode.Should().Be("MC2");
            company.ReferralOffice.Should().Be("REF2");
            company.JurisdictionName.Should().Be("Nevis");
            company.IsActive.Should().BeTrue();

            result.CompanyItems.PageCount.Should().Be(2);
            result.CompanyItems.PageNumber.Should().Be(1);
            result.CompanyItems.PageSize.Should().Be(2);
            result.CompanyItems.TotalItemCount.Should().Be(3);
        }

        [Test]
        public async Task ListCompaniesAsync_FiltersByJurisdiction()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Alpha Company", "AC1", "MC1", "LC1", 2020),
                CreateTestLegalEntity("Beta Corporation", "BC1", "MC2", "LC2", 2021, "23456", "REF2"),
                CreateTestLegalEntity("Gamma Corporation", "GE1", "MC3", "LC3", 2022),
                CreateTestLegalEntity("Delta Corporation", "DI1", "MC4", "LC4", 2023)
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var emptyList = new List<Guid>();
            var request = new ListCompaniesRequest
            {
                SearchTerm = "Corp",
                Active = true,
                PageNumber = 1,
                PageSize = 2,
                AuthorizedJurisdictionIDs = emptyList
            };

            // Act
            var result = await _legalEntitiesDataManager.ListCompaniesAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.CompanyItems.Should().NotBeNull();
            result.CompanyItems.Should().HaveCount(0);
        }
        #endregion
        #region SearchCompaniesWithAnnualFeeStatus Tests
        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_EmptySearchTerm_ReturnsAllCompanies()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = CreateSearchRequest(2023, false);

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.CompanyItems.Should().HaveCount(2);
            result.CompanyItems.Should().Contain(c => c.CompanyName == "Company 1");
            result.CompanyItems.Should().Contain(c => c.CompanyName == "Company 2");
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_FiltersByJurisdiction()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var emptyList = new List<Guid>();
            var request = new SearchCompanyWithAnnualFeeStatusRequest()
            {
                IsPaid = false,
                FinancialYear = 2023,
                PageNumber = 1,
                PageSize = 10,
                AuthorizedJurisdictionIDs = emptyList
            };

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.CompanyItems.Should().HaveCount(0);
        }

        [Test]
        [TestCase("Company 1")]
        [TestCase("C01")]
        [TestCase("MC01")]
        [TestCase("LC1")]
        public async Task SearchCompaniesWithAnnualFeeStatus_WithSearchTerm_ReturnsFilteredCompanies(string searchTerm)
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C01", "MC01", "LC1"),
                CreateTestLegalEntity("Company 2", "C02", "MC02", "LC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = CreateSearchRequest(2023, false, searchTerm);

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.CompanyItems.Should().HaveCount(1);
            result.CompanyItems.Should().Contain(c => c.CompanyName == "Company 1");
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_WrongSearchTerm_ReturnsEmptyList()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = CreateSearchRequest(2023, false, "MC3");

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.CompanyItems.Should().BeEmpty();
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_ChecksPagination()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = CreateSearchRequest(2023, false, pageNumber: 2, pageSize: 1);

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.CompanyItems.Should().HaveCount(1);
            result.CompanyItems.Should().ContainSingle(c => c.CompanyName == "Company 2");
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_ChecksSubmissionMapping()
        {
            // Arrange
            var legalEntity = CreateTestLegalEntity("Company 1", "C1", "MC1", "LC1");
            SetAnnualFeeStatus(legalEntity, 2023, true);
            var submission1 = AddTestSubmission(legalEntity, 2023, true);
            await SeedLegalEntitiesAsync([legalEntity]);

            var request = CreateSearchRequest(financialYear: 2023, isPaid: true);

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            var companyResult = result.CompanyItems.Should().HaveCount(1).And.Subject.Single();
            companyResult.CompanyName.Should().Be("Company 1");
            companyResult.IsPaid.Should().BeTrue();
            companyResult.MasterClientCode.Should().Be("MC1");
            companyResult.CompanyCode.Should().Be("C1");
            companyResult.CompanyId.Should().Be(legalEntity.Id);
            companyResult.CompanyLegacyCode.Should().Be("LC1");
            companyResult.DateSubmissionCreated.Should().Be(submission1.CreatedAt);
            companyResult.DateSubmissionSubmitted.Should().Be(submission1.SubmittedAt);
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_FiltersByFinancialYear()
        {
            // Arrange
            var legalEntity = CreateTestLegalEntity("Company 1", "C1", "MC1");
            SetAnnualFeeStatus(legalEntity, 2023, true);
            SetAnnualFeeStatus(legalEntity, 2024, true);
            await SeedLegalEntitiesAsync([legalEntity]);

            // Act 
            var request = CreateSearchRequest(financialYear: 2023, isPaid: true);

            // Assert
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);
            result.CompanyItems.Should().HaveCount(1).And.Subject.Single().IsPaid.Should().BeTrue();
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_UsesSettingToFilter()
        {
            var legalEntity = CreateTestLegalEntity("Company 1", "C1", "MC1");
            await AddFirstSubmissionYearSetting(legalEntity, 2020);

            SetAnnualFeeStatus(legalEntity, 2019, true); // Before first submission year. Would never happen in real life, but just to test the logic.
            SetAnnualFeeStatus(legalEntity, 2020, true);
            SetAnnualFeeStatus(legalEntity, 2021, false);

            await SeedLegalEntitiesAsync([legalEntity]);

            // Act & Assert for 2019 (before first filing year, so exempt from payment)
            var request2019 = CreateSearchRequest(financialYear: 2019, isPaid: true);
            var result2019 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2019);
            result2019.CompanyItems.Should().HaveCount(0);

            // Act & Assert for 2020 (first filing year, so fee must be paid)
            var request2020 = CreateSearchRequest(financialYear: 2020, isPaid: true);
            var result2020 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2020);
            result2020.CompanyItems.Should().HaveCount(1);

            // Act & Assert for 2021 (after first filing year, so fee must be paid)
            var request2021 = CreateSearchRequest(financialYear: 2021, isPaid: false);
            var result2021 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2021);
            result2021.CompanyItems.Should().HaveCount(1);
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_UsesIncorporationDateToFilter()
        {
            var legalEntity = CreateTestLegalEntity("Company 1", "C1", "MC1", incorporationYear: 2020);

            SetAnnualFeeStatus(legalEntity, 2019, true); // Before incorporation year. Would never happen in real life, but just to test the logic.
            SetAnnualFeeStatus(legalEntity, 2020, true);
            SetAnnualFeeStatus(legalEntity, 2021, true);

            await SeedLegalEntitiesAsync([legalEntity]);

            // Act & Assert for 2019 (before first filing year, so exempt from payment)
            var request2019 = CreateSearchRequest(financialYear: 2019, isPaid: true);
            var result2019 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2019);
            result2019.CompanyItems.Should().HaveCount(0);

            // Act & Assert for 2020 (first filing year, so fee must be paid)
            var request2020 = CreateSearchRequest(financialYear: 2020, isPaid: true);
            var result2020 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2020);
            result2020.CompanyItems.Should().HaveCount(1);

            // Act & Assert for 2021 (after first filing year, so fee must be paid)
            var request2021 = CreateSearchRequest(financialYear: 2021, isPaid: true);
            var result2021 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2021);
            result2021.CompanyItems.Should().HaveCount(1);
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_FiltersByPaidStatus()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2"),
                CreateTestLegalEntity("Company 3", "C3", "MC3")
            };

            SetAnnualFeeStatus(legalEntities[0], 2023, true);
            SetAnnualFeeStatus(legalEntities[1], 2023, false);
            // Company 3 has no annual fee status for 2023

            await SeedLegalEntitiesAsync(legalEntities);

            // Act & Assert for paid = true
            var requestPaid = CreateSearchRequest(financialYear: 2023, isPaid: true);
            var resultPaid = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(requestPaid);

            resultPaid.CompanyItems.Should().HaveCount(1);
            resultPaid.CompanyItems.Should().Contain(c => c.CompanyName == "Company 1" && c.IsPaid);

            // Act & Assert for paid = false
            var requestUnpaid = CreateSearchRequest(financialYear: 2023, isPaid: false);
            var resultUnpaid = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(requestUnpaid);

            resultUnpaid.CompanyItems.Should().HaveCount(2);
            resultUnpaid.CompanyItems.Should().Contain(c => c.CompanyName == "Company 2" && !c.IsPaid);
            resultUnpaid.CompanyItems.Should().Contain(c => c.CompanyName == "Company 3" && !c.IsPaid);
        }
        #endregion
        #region UpdateCompaniesAnnualFeeStatusAsync Tests
        [Test]
        public async Task UpdateCompaniesAnnualFeeStatusAsync_ValidInput_UpdatesAllCompanies()
        {
            // Arrange
            var companies = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2"),
                CreateTestLegalEntity("Company 3", "C3", "MC3")
            };
            await SeedLegalEntitiesAsync(companies);

            var companyIds = companies.Select(c => c.Id).ToList();

            // Act
            await _legalEntitiesDataManager.UpdateCompaniesAnnualFeeStatusAsync(companyIds, 2023, true, _jurisdictionIDs);

            // Assert
            var updatedCompanies = await _legalEntitiesRepository.FindByConditionAsync(c => companyIds.Contains(c.Id));
            foreach (var company in updatedCompanies)
            {
                company.AnnualFees.Should().HaveCount(1);
                var annualFee = company.AnnualFees.Single();
                annualFee.FinancialYear.Should().Be(2023);
                annualFee.IsPaid.Should().BeTrue();
            }
        }

        [Test]
        public async Task UpdateCompaniesAnnualFeeStatusAsync_SomeNonExistentCompanies_ThrowsException()
        {
            // Arrange
            var company = CreateTestLegalEntity("Test Company", "TC1", "MC1");
            await SeedLegalEntitiesAsync([company]);

            var companyIds = new List<Guid> { company.Id, Guid.NewGuid() };

            // Act & Assert
            await _legalEntitiesDataManager
                .Invoking(m => m.UpdateCompaniesAnnualFeeStatusAsync(companyIds, 2023, true, _jurisdictionIDs))
                .Should().ThrowAsync<EntityNotFoundException>();
        }

        [Test]
        public async Task UpdateCompaniesAnnualFeeStatusAsync_EmptyList_DoesNothing()
        {
            // Arrange
            var emptyList = new List<Guid>();

            // Act & Assert
            await _legalEntitiesDataManager
                .Invoking(m => m.UpdateCompaniesAnnualFeeStatusAsync(emptyList, 2023, true, _jurisdictionIDs))
                .Should().NotThrowAsync();
        }
        #endregion

        #region SyncLegalEntitiesAsync Tests
        #region Initial checks
        [Test]
        public async Task SyncLegalEntitiesAsync_WithDuplicateSyncLegalEntity_OnlyProcessesFirst()
        {
            // Arrange

            // Create two SyncLegalEntity objects with same Code but different Names
            var firstSyncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC004", "TEST004");
            firstSyncLegalEntity.Name = "First Company Name";

            var secondSyncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC004", "TEST004");
            secondSyncLegalEntity.Name = "Second Company Name";

            // Act
            await SyncLegalEntities(firstSyncLegalEntity, secondSyncLegalEntity);

            // Assert - Verify only one legal entity was created with the first entity's name
            var createdLegalEntities = await _legalEntitiesRepository.GetQueryable().Where(le => le.Code == "TEST004").ToListAsync();
            createdLegalEntities.Should().HaveCount(1);

            var createdLegalEntity = createdLegalEntities.Single();
            createdLegalEntity.Name.Should().Be("First Company Name", "only the first SyncLegalEntity should be processed");
            createdLegalEntity.Code.Should().Be("TEST004");

            // Assert - Verify only one legal entity history was created with the first entity's name
            var legalEntityHistories = await _legalEntityHistoryRepository.GetQueryable().Where(leh => leh.Code == "TEST004").ToListAsync();
            legalEntityHistories.Should().HaveCount(1);

            var legalEntityHistory = legalEntityHistories.Single();
            legalEntityHistory.Name.Should().Be("First Company Name", "only the first SyncLegalEntity should be processed");
            legalEntityHistory.Status.Should().Be(LegalEntityStatus.Initial);
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_WithUnknownEntityType_ThrowsConstraintException()
        {
            // Arrange
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC002", "TEST002", LegalEntityType.Unknown);

            var request = new SyncLegalEntitiesRequest
            {
                LegalEntities = new List<SyncLegalEntity> { syncLegalEntity }
            };

            // Act & Assert
            var exception = await _legalEntitiesDataManager
                                  .Invoking(m => m.SyncLegalEntitiesAsync(request))
                                  .Should().ThrowAsync<ConstraintException>();

            exception.WithMessage("The EntityType 'Unknown' is not valid");
        }

        #endregion
        
        [Test]
        [Ignore("doesn't work yet, needs to be fixed")] // TODO unignore when fixed
        public async Task SyncLegalEntitiesAsync_SyncInactive_ExistingActive_BasicChanged_UpdatesBasicAndCreatesUpdateReceivedHistory()
        {
            // Arrange - Create an active entity first
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC020", "TEST020");
            await SyncLegalEntities(syncLegalEntity);

            // Make the entity active
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST020");
            createdEntity.SetActive();
            await _legalEntitiesRepository.SaveChangesAsync();

            var initialHistories = await GetHistoriesForLegalEntity("TEST020");

            // Arrange - Change to inactive status with basic data changes
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Closed;
            syncLegalEntity.Name = "Updated Company Name";
            syncLegalEntity.ReferralOffice = "Updated Referral Office";

            // Act - Sync with inactive status and basic changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify entity was updated with new basic data
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST020");
            updatedEntity.Name.Should().Be("Updated Company Name", "entity should be updated with new name");
            updatedEntity.ReferralOffice.Should().Be("Updated Referral Office", "entity should be updated with new referral office");

            // Assert - Verify new history was created with UpdateReceived status
            var histories = await GetHistoriesForLegalEntity("TEST020");
            var newHistories = histories.Except(initialHistories);

            var newHistory = newHistories.Should().ContainSingle("new history record should be created for basic changes on active entity becoming inactive").Subject;
            newHistory.Status.Should().Be(LegalEntityStatus.UpdateReceived, "history status should be UpdateReceived for active entity with basic changes becoming inactive");
            newHistory.Name.Should().Be("Updated Company Name", "history should contain the updated name");
        }

        [Test]
        [Ignore("doesn't work yet, needs to be fixed")] // TODO unignore when fixed
        public async Task SyncLegalEntitiesAsync_SyncInactive_ExistingActive_IncorporationChanged_UpdatesIncorporationAndCreatesUpdateReceivedHistory()
        {
            // Arrange - Create an active entity first
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC021", "TEST021");
            await SyncLegalEntities(syncLegalEntity);

            // Make the entity active
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST021");
            createdEntity.ApproveOnboarding(); // Sets active
            await _legalEntitiesRepository.SaveChangesAsync();
            
            var initialHistories = await GetHistoriesForLegalEntity("TEST021");

            // Arrange - Change to inactive status with incorporation data changes
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Closed;
            syncLegalEntity.IncorporationNr = "UPDATED789";
            syncLegalEntity.IncorporationDate = new DateTime(2024, 6, 15);

            // Act - Sync with inactive status and incorporation changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify entity was updated with new incorporation data
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST021");
            updatedEntity.IncorporationNr.Should().Be("UPDATED789", "entity should be updated with new incorporation number");
            updatedEntity.IncorporationDate.Should().Be(new DateTime(2024, 6, 15), "entity should be updated with new incorporation date");
            updatedEntity.IsActive.Should().BeFalse("entity should be deactivated");

            // Assert - Verify new history was created with UpdateReceived status
            var histories = await GetHistoriesForLegalEntity("TEST021");
            var newHistories = histories.Except(initialHistories);

            var newHistory = newHistories.Should().ContainSingle("new history record should be created for incorporation changes on active entity becoming inactive").Subject;
            newHistory.Status.Should().Be(LegalEntityStatus.UpdateReceived, "history status should be UpdateReceived for active entity with incorporation changes becoming inactive");
            newHistory.IncorporationNr.Should().Be("UPDATED789", "history should contain the updated incorporation number");
            newHistory.IncorporationDate.Should().Be(new DateTime(2024, 6, 15), "history should contain the updated incorporation date");
        }

        [Test] // Note: I am not sure this condition will ever happen in real life
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingActive_IncorporationChanged_ExistingEntityNoHistory_CreatesInitialHistory()
        {
            // Arrange - Create entity without history
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC028", "TEST028");
            await SyncLegalEntities(syncLegalEntity);

            // Make entity active and clear history
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST028");
            createdEntity.SetActive();
            await _legalEntitiesRepository.SaveChangesAsync();

            // Clear existing history
            var existingHistory = await _legalEntityHistoryRepository.GetQueryable().Where(leh => leh.Code == "TEST028").ToListAsync();
            foreach (var history in existingHistory)
            {
                await _legalEntityHistoryRepository.DeleteAsync(history);
            }
            await _legalEntityHistoryRepository.SaveChangesAsync();

            // Arrange - Change incorporation data
            syncLegalEntity.IncorporationNr = "CHANGED999";

            // Act - Sync with incorporation changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify Initial history was created (not UpdateReceived)
            var newHistory = await _legalEntityHistoryRepository.GetQueryable().SingleOrDefaultAsync(leh => leh.Code == "TEST028");
            newHistory.Should().NotBeNull("history should be created");
            newHistory.Status.Should().Be(LegalEntityStatus.Initial, "history status should be Initial when no existing history");
            newHistory.IncorporationNr.Should().Be("CHANGED999", "history should contain updated incorporation number");
        }

        #endregion
        
        #region Master Client changes

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncDifferentMasterClient_ExistingEntity_UpdatesMasterClientAndCreatesActivityLog()
        {
            // Arrange - Create entity with first master client
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC022", "TEST022");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST022");
            var originalMasterClientId = createdEntity.MasterClientId;

            // Arrange - Change to different master client
            syncLegalEntity.MasterClientCode = "MC023";
            syncLegalEntity.MasterClientName = "New Master Client";

            // Act - Sync with different master client
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify master client was updated
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST022");
            updatedEntity.MasterClientId.Should().NotBe(originalMasterClientId, "master client should be updated");
            updatedEntity.MasterClientCode.Should().Be("MC023", "master client code should be updated");

            // Assert - Verify new master client was created
            var newMasterClient = await _masterClientsRepository.GetQueryable().SingleOrDefaultAsync(mc => mc.Code == "MC023");
            newMasterClient.Should().NotBeNull("new master client should be created");
            newMasterClient.Name.Should().Be("New Master Client", "new master client should have correct name");
            newMasterClient.IsActive.Should().BeTrue("new master client should be active");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_NewMasterClient_CreatesActiveMasterClient()
        {
            // Arrange - Create sync entity with new master client code
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC024", "TEST024");

            // Verify master client doesn't exist
            var existingMasterClient = await _masterClientsRepository.GetQueryable().SingleOrDefaultAsync(mc => mc.Code == "MC024");
            existingMasterClient.Should().BeNull("master client should not exist initially");

            // Act - Sync entity with new master client
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify master client was created
            var createdMasterClient = await _masterClientsRepository.GetQueryable().SingleOrDefaultAsync(mc => mc.Code == "MC024");
            createdMasterClient.Should().NotBeNull("master client should be created");
            createdMasterClient.Code.Should().Be("MC024", "master client should have correct code");
            createdMasterClient.Name.Should().Be("Test Master Client", "master client should have correct name");
            createdMasterClient.IsActive.Should().BeTrue("master client should be active");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_ExistingMasterClientInactive_ReactivatesMasterClient()
        {
            // Arrange - Create inactive master client
            var inactiveMasterClient = new MasterClient
            {
                Code = "MC025",
                Name = "Inactive Master Client",
                IsActive = false
            };
            await _masterClientsRepository.InsertAsync(inactiveMasterClient);
            await _masterClientsRepository.SaveChangesAsync();

            // Arrange - Create sync entity with existing inactive master client
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC025", "TEST025");

            // Act - Sync entity with existing inactive master client
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify master client was reactivated
            var reactivatedMasterClient = await _masterClientsRepository.GetQueryable().SingleOrDefaultAsync(mc => mc.Code == "MC025");
            reactivatedMasterClient.Should().NotBeNull("master client should exist");
            reactivatedMasterClient.IsActive.Should().BeTrue("master client should be reactivated");
        }
        #endregion

        #region Error handling and process flow
        [Test]
        public async Task SyncLegalEntitiesAsync_EmptyRequest_DoesNothing()
        {
            // Arrange - Empty legal entities list
            var emptyRequest = new SyncLegalEntitiesRequest
            {
                LegalEntities = new List<SyncLegalEntity>()
            };

            // Act & Assert - Should not throw exception
            await _legalEntitiesDataManager
                .Invoking(m => m.SyncLegalEntitiesAsync(emptyRequest))
                .Should().NotThrowAsync("empty request should be handled gracefully");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_InvalidJurisdiction_ThrowsException()
        {
            // Arrange - Create sync entity with invalid jurisdiction
            var syncLegalEntity = CreateSyncLegalEntity("INVALID", "MC029", "TEST029");

            // Act & Assert - Should throw exception for invalid jurisdiction
            await _legalEntitiesDataManager
                .Invoking(m => m.SyncLegalEntitiesAsync(new SyncLegalEntitiesRequest { LegalEntities = new List<SyncLegalEntity> { syncLegalEntity } }))
                .Should().ThrowAsync<Exception>("invalid jurisdiction should cause an error");
        }

        #endregion

        #region Parameterized Sync Tests

        public class SyncTestCase
        {
            // Test metadata
            public string TestName { get; set; }
            public string Description { get; set; }
            
            // Input parameters
            public string SyncEntityStatusCode { get; set; }  // e.g., LegalEntityStatusCodes.Active, LegalEntityStatusCodes.Closed
            public bool? ExistingEntityIsActive { get; set; }  // null means new entity
            public OnboardingStatus? ExistingEntityOnboardingStatus { get; set; }
            public bool HasBasicChanges { get; set; }
            public bool HasIncorporationChanges { get; set; }
            
            // Expected outcomes
            public bool ExpectBasicUpdated { get; set; }
            public bool ExpectIncorporationUpdated { get; set; }
            public bool ExpectHistoryAdded { get; set; }
            public LegalEntityStatus? ExpectedHistoryStatus { get; set; }
            public bool ExpectActivityLog { get; set; }
            public string ExpectedActivityLogType { get; set; }
            public OnboardingStatus ExpectedOnboardingStatus { get; set; }
            public bool ExpectedIsActive { get; set; }
        }

        [Test]
        [TestCaseSource(nameof(GetSyncTestCases))]
        public async Task SyncLegalEntitiesAsync_WithVariousScenarios_BehavesAsExpected(SyncTestCase testCase)
        {
            // Arrange
            var syncLegalEntity = CreateSyncLegalEntity(
                JurisdictionCodes.Nevis, 
                "MC_TEST",
                "TEST_123",
                entityStatusCode: LegalEntityStatusCodes.Active);

            List<LegalEntityHistory> initialHistories = new();
            List<ActivityLog> initialActivityLogs = new();
            
            // If we're testing with an existing entity, create it first
            if (testCase.ExistingEntityIsActive.HasValue)
            {
                // First sync to create the entity
                await SyncLegalEntities(syncLegalEntity);
                
                // Get the created entity and modify its state as needed
                LegalEntity existingEntity = await _legalEntitiesRepository.GetQueryable()
                                                                           .SingleAsync(le => le.Code == syncLegalEntity.Code);
                
                if (testCase.ExistingEntityIsActive.Value)
                {
                    existingEntity.ApproveOnboarding(); // Sets to active
                }
                
                if (testCase.ExistingEntityOnboardingStatus.HasValue)
                {
                    existingEntity.OnboardingStatus = testCase.ExistingEntityOnboardingStatus.Value;
                }
                
                await _legalEntitiesRepository.SaveChangesAsync();
                
                // Get initial histories and activity logs for comparison later
                initialHistories = (await GetHistoriesForLegalEntity(syncLegalEntity.Code)).ToList();
                initialActivityLogs = (await GetActivityLogsForEntityById(existingEntity.Id)).ToList();
                
                // Apply changes to sync entity if needed
                if (testCase.HasBasicChanges)
                {
                    syncLegalEntity.Name = "Changed Company Name";
                    syncLegalEntity.ReferralOffice = "Changed Referral Office";
                }
                
                if (testCase.HasIncorporationChanges)
                {
                    syncLegalEntity.IncorporationNr = "CHANGED123";
                    syncLegalEntity.IncorporationDate = new DateTime(2024, 6, 15);
                }
            }
            
            // Act
            syncLegalEntity.EntityStatusCode = testCase.SyncEntityStatusCode;
            await SyncLegalEntities(syncLegalEntity);
            
            // Assert
            var resultEntity = await _legalEntitiesRepository.GetQueryable()
                .SingleOrDefaultAsync(le => le.Code == syncLegalEntity.Code);
            
            resultEntity.Should().NotBeNull("entity should be created or updated");
            resultEntity.IsActive.Should().Be(testCase.ExpectedIsActive, 
                $"entity active status should be {testCase.ExpectedIsActive}");
            resultEntity.OnboardingStatus.Should().Be(testCase.ExpectedOnboardingStatus, 
                $"entity onboarding status should be {testCase.ExpectedOnboardingStatus}");
            
            if (testCase.HasBasicChanges && testCase.ExpectBasicUpdated)
            {
                resultEntity.Name.Should().Be("Changed Company Name", 
                    "entity name should be updated when basic changes are expected to be applied");
            }
            
            if (testCase.HasIncorporationChanges && testCase.ExpectIncorporationUpdated)
            {
                resultEntity.IncorporationNr.Should().Be("CHANGED123", 
                    "entity incorporation number should be updated when incorporation changes are expected to be applied");
            }
            
            // Check history records
            var histories = await GetHistoriesForLegalEntity(syncLegalEntity.Code);
            
            if (testCase.ExpectHistoryAdded)
            {
                if (testCase.ExistingEntityIsActive.HasValue)
                {
                    // For existing entities, compare with initial histories
                    var newHistories = histories.Except(initialHistories);
                    
                    newHistories.Should().NotBeEmpty("new history record should be created");
                    var newHistory = newHistories.Should().ContainSingle().Subject;
                    
                    if (testCase.ExpectedHistoryStatus.HasValue)
                    {
                        newHistory.Status.Should().Be(testCase.ExpectedHistoryStatus.Value, 
                            $"history status should be {testCase.ExpectedHistoryStatus}");
                    }
                }
                else
                {
                    // For new entities
                    histories.Should().NotBeEmpty("history record should be created");
                    var history = histories.Should().ContainSingle().Subject;
                    
                    if (testCase.ExpectedHistoryStatus.HasValue)
                    {
                        history.Status.Should().Be(testCase.ExpectedHistoryStatus.Value, 
                            $"history status should be {testCase.ExpectedHistoryStatus}");
                    }
                }
            }
            else
            {
                if (testCase.ExistingEntityIsActive.HasValue)
                {
                    // For existing entities, verify no new histories were added
                    var newHistories = histories.Except(initialHistories);
                    newHistories.Should().BeEmpty("no new history records should be added");
                }
            }
            
            // Check activity logs if expected
            if (testCase.ExpectActivityLog && !string.IsNullOrEmpty(testCase.ExpectedActivityLogType))
            {
                var activityLogs = await GetActivityLogsForEntityById(resultEntity.Id);
                
                if (testCase.ExistingEntityIsActive.HasValue)
                {
                    // For existing entities, compare with initial activity logs
                    var newActivityLogs = activityLogs.Except(initialActivityLogs);
                    newActivityLogs.Should().Contain(al => al.ActivityType == testCase.ExpectedActivityLogType, 
                        $"activity log of type {testCase.ExpectedActivityLogType} should be created");
                }
                else
                {
                    // For new entities, check if master client has activity log
                    var masterClient = await _masterClientsRepository.GetQueryable()
                        .SingleOrDefaultAsync(mc => mc.Code == syncLegalEntity.MasterClientCode);
                    
                    var masterClientActivityLogs = await GetActivityLogsForEntityById(masterClient.Id);
                    masterClientActivityLogs.Should().Contain(al => al.ActivityType == testCase.ExpectedActivityLogType,
                        $"activity log of type {testCase.ExpectedActivityLogType} should be created for master client");
                }
            }
        }

        private static TestCaseData CreateTestCase(SyncTestCase testCase)
        {
            return new TestCaseData(testCase).SetName(testCase.TestName);
        }

        private static IEnumerable<TestCaseData> GetSyncTestCases()
        {
            // New entity tests
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "New_SyncActive_CreatesInactiveWithOnboardingStatusAndInitialHistory",
                Description = "When syncing a new active entity, it should create an inactive entity with Onboarding status and Initial history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = null, // New entity
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = true,
                ExpectIncorporationUpdated = true,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.Initial,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.MasterClientLegalEntityAdded,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });

            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "New_SyncClosing_CreatesInactiveWithOnboardingStatusAndInitialHistory",
                Description = "When syncing a new closing entity, it should create an inactive entity with Onboarding status and Initial history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Closing,
                ExistingEntityIsActive = null, // New entity
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = true,
                ExpectIncorporationUpdated = true,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.Initial,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.MasterClientLegalEntityAdded,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });

            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "New_SyncInactive_CreatesInactiveWithOnboardingUnknownAndInitialHistory",
                Description = "When syncing a new inactive entity, it should create an inactive entity with Unknown status and Initial history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Closed,
                ExistingEntityIsActive = null, // New entity
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = true,
                ExpectIncorporationUpdated = true,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.Initial,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.MasterClientLegalEntityAdded,
                ExpectedOnboardingStatus = OnboardingStatus.Unknown,
                ExpectedIsActive = false
            });

            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "New_SyncMarkedForDeletion_CreatesInactiveWithOnboardingUnknownAndInitialHistory",
                Description = "When syncing a new entity marked for deletion, it should create an inactive entity with Unknown status and Initial history",
                SyncEntityStatusCode = LegalEntityStatusCodes.MarkedForDeletion,
                ExistingEntityIsActive = null, // New entity
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = true,
                ExpectIncorporationUpdated = true,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.Initial,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.MasterClientLegalEntityAdded,
                ExpectedOnboardingStatus = OnboardingStatus.Unknown,
                ExpectedIsActive = false
            });
            
            // Existing inactive entity tests
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncInactive_ExistingInactive_NoChanges_DoesNotCreateAdditionalHistory",
                Description = "When syncing an inactive entity to an existing inactive entity with no changes, it should not create additional history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Closed,
                ExistingEntityIsActive = false,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });

            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncInactive_ExistingInactive_NoChanges_DoesNotCreateAdditionalHistory_MarkedForDeletion",
                Description = "When syncing an entity marked for deletion to an existing inactive entity with no changes, it should not create additional history",
                SyncEntityStatusCode = LegalEntityStatusCodes.MarkedForDeletion,
                ExistingEntityIsActive = false,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                // TODO this should fail after the changes in 1.1 of the sync
                TestName = "SyncInactive_ExistingInactive_BasicChanged_DoesNotApplyChanges",
                Description = "When syncing an inactive entity to an existing inactive entity with basic changes, it should not apply the changes",
                SyncEntityStatusCode = LegalEntityStatusCodes.Closed,
                ExistingEntityIsActive = false,
                HasBasicChanges = true,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });

            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncInactive_ExistingInactive_BasicChanged_DoesNotApplyChanges_MarkedForDeletion",
                Description = "When syncing an entity marked for deletion to an existing inactive entity with basic changes, it should not apply the changes",
                SyncEntityStatusCode = LegalEntityStatusCodes.MarkedForDeletion,
                ExistingEntityOnboardingStatus = OnboardingStatus.Unknown,
                ExistingEntityIsActive = false,
                HasBasicChanges = true,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Unknown,
                ExpectedIsActive = false
            });
            
            // Existing active entity tests
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingActive_NoChanges_DoesNotCreateAdditionalHistory",
                Description = "When syncing an active entity to an existing active entity with no changes, it should not create additional history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = true,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Approved,
                ExpectedIsActive = true
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingActive_BasicChanged_UpdatesBasicAndCreatesConfirmedHistory",
                Description = "When syncing an active entity to an existing active entity with basic changes, it should update basic data and create Confirmed history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = true,
                HasBasicChanges = true,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = true,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.Confirmed,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Approved,
                ExpectedIsActive = true
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingActive_IncorporationChanged_UpdatesIncorporationAndCreatesUpdateReceivedHistory",
                Description = "When syncing an active entity to an existing active entity with incorporation changes, it should update incorporation data and create UpdateReceived history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = true,
                HasBasicChanges = false,
                HasIncorporationChanges = true,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = true,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.UpdateReceived,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Approved,
                ExpectedIsActive = true
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingActive_BothBasicAndIncorporationChanged_UpdatesAndCreatesUpdateReceivedHistory",
                Description = "When syncing an active entity to an existing active entity with both basic and incorporation changes, it should update both and create UpdateReceived history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = true,
                HasBasicChanges = true,
                HasIncorporationChanges = true,
                ExpectBasicUpdated = true,
                ExpectIncorporationUpdated = true,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.UpdateReceived,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Approved,
                ExpectedIsActive = true
            });
            
            // Status transition tests
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncInactive_ExistingActiveOnboarding_SetsClosedWhileOnboarding",
                Description = "When syncing an inactive entity to an existing active entity with Onboarding status, it should set ClosedWhileOnboarding status",
                SyncEntityStatusCode = LegalEntityStatusCodes.Closed,
                ExistingEntityIsActive = true,
                ExistingEntityOnboardingStatus = OnboardingStatus.Onboarding,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.CompanyDeactivated,
                ExpectedOnboardingStatus = OnboardingStatus.ClosedWhileOnboarding,
                ExpectedIsActive = false
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncInactive_ExistingActiveApproved_DeactivatesEntityKeepsStatus",
                Description = "When syncing an inactive entity to an existing active entity with Approved status, it should deactivate the entity but keep the status",
                SyncEntityStatusCode = LegalEntityStatusCodes.Closed,
                ExistingEntityIsActive = true,
                ExistingEntityOnboardingStatus = OnboardingStatus.Approved,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.CompanyDeactivated,
                ExpectedOnboardingStatus = OnboardingStatus.Approved,
                ExpectedIsActive = false
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncInactive_ExistingActiveWithMarkedForDeletion_DeactivatesEntity",
                Description = "When syncing an inactive entity with MarkedForDeletion status to an existing active entity, it should deactivate the entity",
                SyncEntityStatusCode = LegalEntityStatusCodes.MarkedForDeletion,
                ExistingEntityIsActive = true,
                ExistingEntityOnboardingStatus = OnboardingStatus.Approved,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.CompanyDeactivated,
                ExpectedOnboardingStatus = OnboardingStatus.Approved,
                ExpectedIsActive = false
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingInactiveApproved_ChangesToOnboarding",
                Description = "When syncing an active entity to an existing inactive entity with Approved status, it should change to Onboarding status",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = false,
                ExistingEntityOnboardingStatus = OnboardingStatus.Approved,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.CompanyOnboardingChanged,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingInactiveClosedWhileOnboarding_ChangesToOnboarding",
                Description = "When syncing an active entity to an existing inactive entity with ClosedWhileOnboarding status, it should change to Onboarding status",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = false,
                ExistingEntityOnboardingStatus = OnboardingStatus.ClosedWhileOnboarding,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.CompanyOnboardingChanged,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingInactiveDeclined_ChangesToOnboarding",
                Description = "When syncing an active entity to an existing inactive entity with Declined status, it should change to Onboarding status",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = false,
                ExistingEntityOnboardingStatus = OnboardingStatus.Declined,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.CompanyOnboardingChanged,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingInactiveOnboarding_KeepsOnboardingStatus",
                Description = "When syncing an active entity to an existing inactive entity with Onboarding status, it should keep the Onboarding status",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = false,
                ExistingEntityOnboardingStatus = OnboardingStatus.Onboarding,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });
            
            // Active to inactive with changes tests
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingInactive_BasicChanged_CreatesUpdateReceivedHistory",
                Description = "When syncing an active entity to an existing inactive entity with basic changes, it should create UpdateReceived history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = false,
                HasBasicChanges = true,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = true,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.UpdateReceived,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingInactive_IncorporationChanged_CreatesUpdateReceivedHistory",
                Description = "When syncing an active entity to an existing inactive entity with incorporation changes, it should create UpdateReceived history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = false,
                HasBasicChanges = false,
                HasIncorporationChanges = true,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = true,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.UpdateReceived,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });
            
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingInactive_BothBasicAndIncorporationChanged_CreatesUpdateReceivedHistory",
                Description = "When syncing an active entity to an existing inactive entity with both basic and incorporation changes, it should create UpdateReceived history",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = false,
                HasBasicChanges = true,
                HasIncorporationChanges = true,
                ExpectBasicUpdated = true,
                ExpectIncorporationUpdated = true,
                ExpectHistoryAdded = true,
                ExpectedHistoryStatus = LegalEntityStatus.UpdateReceived,
                ExpectActivityLog = false,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });

            // Activity log specific tests
            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncActive_ExistingInactiveApproved_CreatesOnboardingChangedActivityLog",
                Description = "When syncing an active entity to an existing inactive entity with Approved status, it should create an onboarding changed activity log",
                SyncEntityStatusCode = LegalEntityStatusCodes.Active,
                ExistingEntityIsActive = false,
                ExistingEntityOnboardingStatus = OnboardingStatus.Approved,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.CompanyOnboardingChanged,
                ExpectedOnboardingStatus = OnboardingStatus.Onboarding,
                ExpectedIsActive = false
            });

            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncClosed_ExistingActive_CreatesDeactivatedActivityLog",
                Description = "When syncing a closed entity to an existing active entity, it should create a deactivated activity log",
                SyncEntityStatusCode = LegalEntityStatusCodes.Closed,
                ExistingEntityIsActive = true,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.CompanyDeactivated,
                ExpectedOnboardingStatus = OnboardingStatus.Approved,
                ExpectedIsActive = false
            });

            yield return CreateTestCase(new SyncTestCase
            {
                TestName = "SyncMarkedForDeletion_ExistingActive_CreatesDeactivatedActivityLog",
                Description = "When syncing a closed entity to an existing active entity, it should create a deactivated activity log",
                SyncEntityStatusCode = LegalEntityStatusCodes.MarkedForDeletion,
                ExistingEntityIsActive = true,
                HasBasicChanges = false,
                HasIncorporationChanges = false,
                ExpectBasicUpdated = false,
                ExpectIncorporationUpdated = false,
                ExpectHistoryAdded = false,
                ExpectActivityLog = true,
                ExpectedActivityLogType = ActivityLogActivityTypes.CompanyDeactivated,
                ExpectedOnboardingStatus = OnboardingStatus.Approved,
                ExpectedIsActive = false
            });
        }
        
        #endregion

        #region Property Mapping Tests

        [Test]
        public async Task SyncLegalEntitiesAsync_NewEntity_MapsAllPropertiesCorrectly()
        {
            // Arrange
            var syncLegalEntity = CreateComprehensiveSyncLegalEntity(
                JurisdictionCodes.Nevis,
                "MC_PROP_TEST",
                "PROP_TEST_001");

            // Act
            await SyncLegalEntities(syncLegalEntity);

            // Assert
            var createdEntity = await _legalEntitiesRepository.GetQueryable()
                .SingleOrDefaultAsync(le => le.Code == "PROP_TEST_001");

            createdEntity.Should().NotBeNull("entity should be created");

            // Verify all basic properties are mapped correctly
            VerifyAllPropertiesMappedCorrectly(createdEntity, syncLegalEntity);
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_ExistingEntity_UpdatesAllPropertiesCorrectly()
        {
            // Arrange - Create initial entity
            var initialSyncEntity = CreateComprehensiveSyncLegalEntity(
                JurisdictionCodes.Nevis,
                "MC_PROP_UPD",
                "PROP_UPD_001");

            await SyncLegalEntities(initialSyncEntity);

            // Make the entity active so updates will be applied
            var createdEntity = await _legalEntitiesRepository.GetQueryable()
                .SingleAsync(le => le.Code == "PROP_UPD_001");
            createdEntity.SetActive();
            await _legalEntitiesRepository.SaveChangesAsync();

            // Arrange - Create updated sync entity with all properties changed
            var updatedSyncEntity = CreateComprehensiveSyncLegalEntity(
                JurisdictionCodes.Nevis,
                "MC_PROP_UPD",
                "PROP_UPD_001",
                isUpdate: true);

            // Act
            await SyncLegalEntities(updatedSyncEntity);

            // Assert
            var updatedEntity = await _legalEntitiesRepository.GetQueryable()
                .SingleOrDefaultAsync(le => le.Code == "PROP_UPD_001");

            updatedEntity.Should().NotBeNull("entity should exist");

            // Verify all updatable properties are updated correctly
            VerifyUpdatablePropertiesMappedCorrectly(updatedEntity, updatedSyncEntity, initialSyncEntity);
        }

        private static void VerifyAllPropertiesMappedCorrectly(LegalEntity legalEntity, SyncLegalEntity syncLegalEntity)
        {
            // Basic properties
            legalEntity.Name.Should().Be(syncLegalEntity.Name, "Name should be mapped correctly");
            legalEntity.Code.Should().Be(syncLegalEntity.Code, "Code should be mapped correctly");
            legalEntity.LegacyCode.Should().Be(syncLegalEntity.LegacyCode, "LegacyCode should be mapped correctly");
            legalEntity.EntityType.Should().Be(syncLegalEntity.EntityType, "EntityType should be mapped correctly");
            legalEntity.EntityTypeCode.Should().Be(syncLegalEntity.EntityTypeCode, "EntityTypeCode should be mapped correctly");
            legalEntity.EntityTypeName.Should().Be(syncLegalEntity.EntityTypeName, "EntityTypeName should be mapped correctly");
            legalEntity.ReferralOffice.Should().Be(syncLegalEntity.ReferralOffice, "ReferralOffice should be mapped correctly");
            legalEntity.ProductionOffice.Should().Be(syncLegalEntity.ProductionOffice, "ProductionOffice should be mapped correctly");
            legalEntity.EntityStatus.Should().Be(syncLegalEntity.EntityStatus, "EntityStatus should be mapped correctly");
            legalEntity.EntitySubStatus.Should().Be(syncLegalEntity.EntitySubStatus, "EntitySubStatus should be mapped correctly");
            legalEntity.RiskGroup.Should().Be(syncLegalEntity.RiskGroup, "RiskGroup should be mapped correctly");
            legalEntity.MasterClientCode.Should().Be(syncLegalEntity.MasterClientCode, "MasterClientCode should be mapped correctly");
            legalEntity.ExternalUniqueId.Should().Be(syncLegalEntity.UniqueId, "ExternalUniqueId should be mapped from UniqueId");

            // Incorporation properties
            legalEntity.IncorporationNr.Should().Be(syncLegalEntity.IncorporationNr, "IncorporationNr should be mapped correctly");
            legalEntity.IncorporationDate.Should().Be(syncLegalEntity.IncorporationDate, "IncorporationDate should be mapped correctly");

            // TODO Note: JurisdictionOfRegistration mapping depends on context (JurisdictionCode vs JurisdictionVPCode)
            // For new entities, it uses JurisdictionCode; for updates, it uses JurisdictionVPCode
            // We'll verify it's not null and contains expected jurisdiction info
            legalEntity.JurisdictionOfRegistration.Should().NotBeNullOrEmpty("JurisdictionOfRegistration should be mapped");
        }

        private static void VerifyUpdatablePropertiesMappedCorrectly(LegalEntity legalEntity, SyncLegalEntity updatedSyncEntity, SyncLegalEntity originalSyncEntity)
        {
            // Basic properties that are updated during sync
            legalEntity.Name.Should().Be(updatedSyncEntity.Name, "Name should be updated correctly");
            legalEntity.Code.Should().Be(updatedSyncEntity.Code, "Code should be updated correctly");
            legalEntity.LegacyCode.Should().Be(updatedSyncEntity.LegacyCode, "LegacyCode should be updated correctly");
            legalEntity.EntityType.Should().Be(updatedSyncEntity.EntityType, "EntityType should be updated correctly");
            legalEntity.EntityTypeCode.Should().Be(updatedSyncEntity.EntityTypeCode, "EntityTypeCode should be updated correctly");
            legalEntity.EntityTypeName.Should().Be(updatedSyncEntity.EntityTypeName, "EntityTypeName should be updated correctly");
            legalEntity.ReferralOffice.Should().Be(updatedSyncEntity.ReferralOffice, "ReferralOffice should be updated correctly");
            legalEntity.ProductionOffice.Should().Be(updatedSyncEntity.ProductionOffice, "ProductionOffice should be updated correctly");
            legalEntity.EntityStatus.Should().Be(updatedSyncEntity.EntityStatus, "EntityStatus should be updated correctly");
            legalEntity.EntitySubStatus.Should().Be(updatedSyncEntity.EntitySubStatus, "EntitySubStatus should be updated correctly");
            legalEntity.RiskGroup.Should().Be(updatedSyncEntity.RiskGroup, "RiskGroup should be updated correctly");
            legalEntity.MasterClientCode.Should().Be(updatedSyncEntity.MasterClientCode, "MasterClientCode should be updated correctly");

            // Incorporation properties that are updated during sync
            legalEntity.IncorporationNr.Should().Be(updatedSyncEntity.IncorporationNr, "IncorporationNr should be updated correctly");
            legalEntity.IncorporationDate.Should().Be(updatedSyncEntity.IncorporationDate, "IncorporationDate should be updated correctly");

            // Properties that are NOT updated during sync (only set during creation)
            legalEntity.ExternalUniqueId.Should().Be(originalSyncEntity.UniqueId, "ExternalUniqueId should remain unchanged from original creation");

            // JurisdictionOfRegistration should be updated
            legalEntity.JurisdictionOfRegistration.Should().NotBeNullOrEmpty("JurisdictionOfRegistration should be mapped");
        }

        private static SyncLegalEntity CreateComprehensiveSyncLegalEntity(
            string jurisdictionCode,
            string masterClientCode,
            string entityCode,
            bool isUpdate = false)
        {
            var suffix = isUpdate ? "_UPDATED" : "";

            return new SyncLegalEntity
            {
                JurisdictionCode = jurisdictionCode,
                JurisdictionVPCode = jurisdictionCode + "_VP",
                UniqueId = entityCode + "_UNIQUE" + suffix,
                EntityType = LegalEntityType.Company,
                MasterClientCode = masterClientCode,
                MasterClientName = "Test Master Client" + suffix,
                Code = entityCode,
                Name = "Comprehensive Test Company" + suffix,
                IncorporationNr = "INC" + entityCode + suffix,
                IncorporationDate = isUpdate ? new DateTime(2024, 6, 15) : new DateTime(2023, 1, 15),
                LegacyCode = "LEG" + entityCode + suffix,
                ReferralOffice = "REF" + (isUpdate ? "2" : "1"),
                ProductionOffice = "PROD" + (isUpdate ? "2" : "1"),
                EntityStatusCode = LegalEntityStatusCodes.Active,
                EntityStatus = LegalEntityStatusNames.Active + suffix,
                EntitySubStatusCode = "SUB" + (isUpdate ? "2" : "1"),
                EntitySubStatus = "SubStatus" + suffix,
                RiskGroup = "Risk" + (isUpdate ? "High" : "Low"),
                Administrator = "Admin" + suffix,
                Manager = "Manager" + suffix,
                EntityTypeCode = "CI",
                EntityTypeName = LegalEntityTypes.IBC
            };
        }

        #endregion
        #endregion
        private async Task<IEnumerable<ActivityLog>> GetActivityLogsForEntityById(Guid entityId)
        {
            var activityLogs = await _activityLogRepository.GetQueryable()
                .Where(al => al.EntityId == entityId)
                .ToListAsync();

            return activityLogs;
        }

        private async Task SyncLegalEntities(params SyncLegalEntity[] syncLegalEntities)
        {
            // Act
            await _legalEntitiesDataManager.SyncLegalEntitiesAsync(new SyncLegalEntitiesRequest()
            {
                LegalEntities = syncLegalEntities.ToList()
            });
        }

        private async Task AddFirstSubmissionYearSetting(LegalEntity legalEntity, int financialYear)
        {
            await _settingsManager.SaveSettingsForCompanyAsync(new SubmissionSettingsDTO { FirstSubmissionYear = financialYear },
                legalEntity.Id);
        }

        private SyncLegalEntity CreateSyncLegalEntity(string jurisdictionCode, string masterClientCode, string entityCode,
            LegalEntityType entityType = LegalEntityType.Company, string entityStatusCode = LegalEntityStatusCodes.Active,
            string entityStatus = LegalEntityStatusNames.Active)
        {
            return new SyncLegalEntity
            {
                JurisdictionCode = jurisdictionCode,
                UniqueId = entityCode,
                EntityType = entityType,
                MasterClientCode = masterClientCode,
                MasterClientName = "Test Master Client",
                Code = entityCode,
                Name = "Test Company",
                IncorporationNr = "12345",
                LegacyCode = $"LEG{entityCode}",
                ReferralOffice = "REF1",
                EntityStatusCode = entityStatusCode,
                EntityStatus = entityStatus,
                EntityTypeCode = "CI",
                EntityTypeName = LegalEntityTypes.IBC
            };
        }

        private LegalEntity CreateTestLegalEntity(string name, string code, string masterClientCode, string legacyCode = null, int incorporationYear = 2000, string incorporationNr = "1234560", string referralOffice = "REF1", OnboardingStatus onboardingStatus = OnboardingStatus.Onboarding)
        {
            var jurisdiction = _jurisdictionsRepository.GetQueryable().First();

            return new LegalEntity(Guid.NewGuid())
            {
                Name = name,
                Code = code,
                LegacyCode = legacyCode,
                EntityType = LegalEntityType.Company,
                MasterClientCode = masterClientCode,
                MasterClient = new MasterClient { Code = masterClientCode },
                IncorporationDate = new DateTime(incorporationYear, 01, 01),
                IncorporationNr = incorporationNr,
                ReferralOffice = referralOffice,
                Jurisdiction = jurisdiction,
                OnboardingStatus = onboardingStatus,
                EntityTypeName = LegalEntityTypes.IBC
            };
        }

        private static void SetAnnualFeeStatus(LegalEntity legalEntity, int financialYear, bool isPaid)
        {
            legalEntity.UpdateAnnualFeeStatus(financialYear, isPaid);
        }

        private static Submission AddTestSubmission(LegalEntity legalEntity, int financialYear, bool isPaid)
        {
            var id = Guid.NewGuid();
            var submission = new Submission(id)
            {
                Name = $"Submission {financialYear}",
                FinancialYear = financialYear,
                CreatedAt = DateTime.UtcNow,
                SubmittedAt = DateTime.UtcNow.AddDays(1),
                ReportId = id.ToString(),
                IsPaid = isPaid,
                Layout = LayoutConsts.TridentTrust
            };

            legalEntity.Submissions.Add(submission);

            return submission;
        }

        private async Task SeedLegalEntitiesAsync(List<LegalEntity> legalEntities)
        {
            await _legalEntitiesRepository.InsertAsync(legalEntities);
            await _legalEntitiesRepository.SaveChangesAsync();
        }

        private SearchCompanyWithAnnualFeeStatusRequest CreateSearchRequest(int financialYear, bool isPaid,
            string searchTerm = "", int pageNumber = 1, int pageSize = 10)
        {
            return new SearchCompanyWithAnnualFeeStatusRequest
            {
                SearchTerm = searchTerm,
                FinancialYear = financialYear,
                PageNumber = pageNumber,
                PageSize = pageSize,
                IsPaid = isPaid,
                AuthorizedJurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList()
            };
        }
        
        private async Task<IEnumerable<LegalEntityHistory>> GetHistoriesForLegalEntity(string code)
        {
            return (await _legalEntityHistoryRepository.FindByConditionAsync(leh => leh.Code == code)).ToList();
        }
    }
}
