// <copyright file="PermissionHandler.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Permissions;

namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Handler for permissions.
    /// </summary>
    public class PermissionHandler
    {
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="PermissionHandler"/> class.
        /// </summary>
        /// <param name="securityManager">The security manager for getting roles etc.</param>
        public PermissionHandler(ISecurityManager securityManager)
        {
            _securityManager = securityManager;
        }

        /// <summary>
        /// Checks if the current user has the given permission (see <see cref="WellKnownPermissionNames"/>).
        /// </summary>
        /// <remarks>
        /// This is done by getting all roles that have that permission and then check for the role.
        /// </remarks>
        /// <param name="permission">Permission to check.</param>
        /// <returns>Returns true if user has permssion.</returns>
        public async Task<bool> HasPermissionAsync(string permission)
        {
            await Task.CompletedTask;

            var permissionsWithRoles = _securityManager.GetPermissionsWithRoles();

            if (!permissionsWithRoles.TryGetValue(permission, out IList<string> possibleRoles))
            {
                return false;
            }

            return await _securityManager.UserHasOneOfApplicationRolesAsync(possibleRoles);
        }
    }
}
